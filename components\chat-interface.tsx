"use client"

import { useEffect, useRef, useState } from "react"
import { useSelector, useDispatch } from "react-redux"
import { useRouter } from "next/navigation"
import type { RootState, AppDispatch } from "@/store"
import { setInputValue, processUserMessage, clearInputValue } from "@/store/chatSlice"
import { setActiveQueryId } from "@/store/queriesSlice"
import { setActiveTab, setChatView } from "@/store/uiSlice"
import { connectWebSocket, sendWebSocketMessage, selectConnectionStatus, selectIsConnected } from "@/store/websocketSlice"

import { ChatHeader } from "./chat-header"
import { SourcesPanel } from "./sources-panel"
import { ChatInputSection } from "./chat-input-section"
import { MessageHistory } from "./message-history"
import { ConnectionStatus } from "./connection-status"

const initialSuggestedQueries = [
  "Recommend a valve with electrical wiring specification 24V DC, function 5/2 double solenoid, poppet material aluminum, and mounting on sub-base.",
  "What are the key differences in flow capacity (Cv) between the Classic 27 Series poppet valves and the 21 Series poppet valves for similar port sizes?",
  "What are the technical specifications of the SV27 Series valve?",
  "Which ROSS valve would you recommend for a hazardous location application with ambient temperatures up to 30°C, requiring chemical-resistant seals and a durable, compact design?",
  "Which ROSS directional control valve would you recommend for a standard application requiring 3/2 single solenoid function, operating reliably within an ambient temperature range of 39°F to 122°F, and built to withstand filtered air environments with base mounting and NPT threading?",
]

const relatedTriggers = [
  "Can you tell me a bit more about the above product(s)?",
  "What else should I know about the above product(s)?",
  "Are there any key advantages of the above product(s) I should be aware of?",
  "Is there anything specific that makes the above product(s) stand out?",
  "Can you explain a little more about how the above product(s) would work in practice?",
]

export function ChatInterface() {
  const dispatch = useDispatch<AppDispatch>()
  const router = useRouter()
  const messagesEndRef = useRef<null | HTMLDivElement>(null)
  const [randomTriggers, setRandomTriggers] = useState([...relatedTriggers].sort(() => 0.5 - Math.random()).slice(0, 2));

  const inputValue = useSelector((state: RootState) => state.chat.inputValue)
  const { activeQueryId, queries } = useSelector((state: RootState) => state.queries)
  const activeQuery = queries.find((q: any) => q.id === activeQueryId)
  const currentMessages = activeQuery?.messages || []

  // WebSocket state
  const connectionStatus = useSelector(selectConnectionStatus)
  const isConnected = useSelector(selectIsConnected)
  const sourcesConfig = useSelector((state: RootState) => state.sources.sources)

  const isProcessing = currentMessages.some(
    (msg: any) => msg.type === "assistant" && (
      msg.thinkingState?.status === "processing" ||
      (msg.thinkingSteps && Object.values(msg.thinkingSteps).some((step: any) => step.status === 'in-progress'))
    )
  )

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(scrollToBottom, [currentMessages])

  // Initialize WebSocket connection on component mount
  useEffect(() => {
    dispatch(connectWebSocket())
  }, [dispatch])

  const handleSend = async () => {
    if (!inputValue.trim()) return

    // Process via existing flow to create query and user message
    const resultAction = await dispatch(processUserMessage(inputValue))
    if (processUserMessage.fulfilled.match(resultAction)) {
      const { queryId, isNewQuery } = resultAction.payload

      // Create placeholder assistant message for WebSocket processing
      const { addMessageToQuery } = await import("@/store/queriesSlice")
      const assistantPlaceholderMessage = {
        id: `ws-${Date.now()}`,
        type: "assistant" as const,
        content: "", // Will be filled by WebSocket response
        timestamp: new Date().toISOString(),
        thinkingState: { status: "processing" as const, steps: [] }
      }

      dispatch(addMessageToQuery({
        queryId: queryId as string,
        message: assistantPlaceholderMessage
      }))

      // Send message via WebSocket
      const sources = {
        web: sourcesConfig.linkedin.enabled,
        database: sourcesConfig.rossDatabase.enabled,
        unstructured: sourcesConfig.email.enabled,
        feedback: sourcesConfig.feedback?.enabled || true,
      }

      dispatch(sendWebSocketMessage({
        query: inputValue,
        sources
      }))

      if (isNewQuery) {
        router.push(`/queries/${queryId}`)
      }
    }
    dispatch(clearInputValue())
  }

  const handleSuggestedClick = (query: string) => {
    dispatch(setInputValue(query))
  }

  const handleNewQuery = () => {
    dispatch(setActiveQueryId(null))
    dispatch(setActiveTab("chat"))
    dispatch(setChatView("chat"))
    router.push("/")
  }

  const showInitialChatScreen = !activeQueryId && currentMessages.length === 0 && !isProcessing

  return (
    <div className="flex flex-col h-full">
      {showInitialChatScreen ? (
        <div className="flex-grow flex flex-col justify-end mb-5">
          <ChatHeader username="James" />
          <SourcesPanel />
        </div>
      ) : (
        activeQuery && (
          <div className="flex-grow overflow-y-auto space-y-8 scrollbar">
            <MessageHistory messages={currentMessages} />
            <div ref={messagesEndRef} />
          </div>
        )
      )}

      {/* Input section with more spacing */}
      <div className="mt-auto pt-px">
        <ConnectionStatus />
        <ChatInputSection
          inputValue={inputValue}
          onInputChange={(val) => dispatch(setInputValue(val))}
          onSend={handleSend}
          onSuggestedClick={handleSuggestedClick}
          placeholder={
            !isConnected
              ? `Connecting to server... (${connectionStatus})`
              : isProcessing
                ? "Agent is thinking..."
                : activeQueryId
                  ? "Ask a follow-up question..."
                  : "Ask anything about Ross Products, Sales Processes, or Technical Specs"
          }
          suggestedQueries={activeQueryId ? randomTriggers : initialSuggestedQueries}
          relatedTriggers={!!activeQueryId}
          disabled={isProcessing || !isConnected}
        />
      </div>
    </div>
  )
}
